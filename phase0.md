Oczywiście. Poniżej znajduje się profesjonalny Dokument Wymagań Produktu (Product Requirements Document - PRD), kt<PERSON>ry moż<PERSON><PERSON> wykorzystać jako podstawę do implementacji swojej aplikacji. Dokument ten strukturyzuje wszystkie Twoje potrzeby w standardzie używanym w branży IT do tworzenia oprogramowania.

---

### **Dokument Wymagań Produktu (PRD): Aplikacja Księgowa zintegrowana z KSeF**

**Wersja:** 1.0
**Data:** 31.07.2025
**Autor:** Gemini AI (na podstawie wymagań użytkownika)

---

### 1. Wprowadzenie i Cel Produktu

#### 1.1. Problem
Obowiązkowy Krajowy System e-Faktur (KSeF) centralizuje obieg faktur, ale nie oferuje narzędzi do pełnej obsługi księgowej. Przedsiębiorcy, zwłaszcza samozatrudnieni i małe firmy (np. programiści na ryczałcie), potrzebują zintegrowanego rozwiązania, które nie tylko obsłuży KSeF, ale również zautomatyzuje kluczowe procesy księgowe: obliczanie podatków (PIT, VAT) i składek ZUS. Ręczne przenoszenie danych między systemami jest czasochłonne i podatne na błędy.

#### 1.2. Rozwiązanie i Wizja Produktu
Stworzenie aplikacji desktopowej/webowej, która będzie kompleksowym centrum zarządzania jednoosobową działalnością gospodarczą. Aplikacja, wykorzystując API KSeF jako główne źródło danych o przychodach i kosztach, zautomatyzuje procesy fakturowania oraz generowania miesięcznych podsumowań księgowych, znacząco upraszczając i przyspieszając obowiązki przedsiębiorcy.

#### 1.3. Zakres Produktu
*   **W zakresie (In-Scope):**
    *   Pełna dwukierunkowa integracja z API KSeF.
    *   Wystawianie faktur przychodowych.
    *   Automatyczne pobieranie faktur kosztowych.
    *   Kalkulacja miesięcznych zobowiązań podatkowych (PIT, VAT).
    *   Kalkulacja miesięcznych składek ZUS, w tym dynamiczne wyliczanie składki zdrowotnej dla ryczałtu na podstawie progów przychodowych.
    *   Generowanie podsumowań i raportów gotowych do wprowadzenia w systemach rządowych.

*   **Poza zakresem (Out-of-Scope) dla wersji 1.0:**
    *   Automatyczne wysyłanie deklaracji do systemów e-Deklaracje (Ministerstwo Finansów) oraz PUE ZUS. Aplikacja generuje dane, które użytkownik musi samodzielnie wprowadzić.
    *   Obsługa kadr i płac (umowy o pracę, zlecenia).
    *   Obsługa innych form opodatkowania niż ryczałt od przychodów ewidencjonowanych (w pierwszej wersji).
    *   Integracja z systemami bankowymi.

---

### 2. Persony Użytkowników

*   **Persona Główna: "Jan, Programista na Ryczałcie"**
    *   **Kim jest:** Samozatrudniony programista (B2B), rozliczający się na ryczałcie od przychodów ewidencjonowanych, płatnik VAT.
    *   **Potrzeby:** Chce maksymalnie zautomatyzować "papierologię", aby skupić się na pracy. Potrzebuje prostego narzędzia, które powie mu: "wystaw fakturę tutaj", "tyle podatku zapłać", "tyle ZUS-u przelej".
    *   **Frustracje:** Comiesięczne logowanie się do różnych systemów, przepisywanie kwot, niepewność, czy dobrze obliczył progi składki zdrowotnej ZUS.

---

### 3. Wymagania Funkcjonalne (Epiki i Historyjki Użytkownika)

#### **Epic 1: Zarządzanie Połączeniem z KSeF**
*Jako użytkownik, chcę bezpiecznie połączyć aplikację z moim kontem KSeF, aby mogła ona automatycznie zarządzać moimi fakturami.*

*   **User Story 1.1:** Chcę przejść przez proces autoryzacji z KSeF (z użyciem tokena lub podpisu kwalifikowanego), aby aplikacja uzyskała dostęp do API.
    *   **Kryteria akceptacji:**
        *   Aplikacja obsługuje proces autoryzacji (challenge-response).
        *   Tokeny dostępowe są bezpiecznie przechowywane (np. z użyciem systemowego pęku kluczy).
        *   W interfejsie użytkownika widoczny jest status połączenia z KSeF (aktywne/nieaktywne).

#### **Epic 2: Fakturowanie Sprzedaży**
*Jako użytkownik, chcę w prosty sposób wystawiać faktury, które będą automatycznie wysyłane do KSeF.*

*   **User Story 2.1:** Chcę wypełnić prosty formularz z danymi faktury (nabywca, pozycje, kwoty), a aplikacja wygeneruje poprawny plik XML i wyśle go do KSeF.
    *   **Kryteria akceptacji:**
        *   Formularz zawiera walidację danych (np. format NIP, poprawność kwot).
        *   Aplikacja poprawnie wywołuje endpointy sesji interaktywnej (`/api/v2/sessions/online`).
        *   Po wysłaniu faktury, aplikacja pobiera i wyświetla status przetwarzania oraz numer KSeF.
        *   Użytkownik otrzymuje UPO (Urzędowe Poświadczenie Odbioru) dla wysłanej faktury.

#### **Epic 3: Zarządzanie Kosztami**
*Jako użytkownik, chcę, aby moje faktury kosztowe automatycznie pojawiały się w aplikacji, bez potrzeby ręcznego wprowadzania.*

*   **User Story 3.1:** Aplikacja powinna cyklicznie (np. raz dziennie) sprawdzać KSeF w poszukiwaniu nowych faktur, na których jestem nabywcą.
    *   **Kryteria akceptacji:**
        *   Aplikacja wykorzystuje endpoint `invoices/query` do wyszukiwania faktur zakupowych.
        *   Pobrane faktury są parsowane z formatu XML i wyświetlane w czytelnej liście kosztów.
        *   Użytkownik ma możliwość kategoryzacji kosztów (np. koszty operacyjne, zakup sprzętu).

#### **Epic 4: Miesięczne Rozliczenia Podatkowe (PIT i VAT)**
*Jako użytkownik, chcę co miesiąc widzieć gotowe wyliczenie podatku VAT i zaliczki na podatek dochodowy (PIT), abym wiedział, ile przelać do Urzędu Skarbowego.*

*   **User Story 4.1:** Aplikacja powinna prezentować pulpit (dashboard) z podsumowaniem miesiąca, pokazujący sumę przychodów i kosztów (z faktur VAT), a na tej podstawie obliczony podatek VAT należny i naliczony.
    *   **Kryteria akceptacji:**
        *   Aplikacja poprawnie sumuje kwoty netto i VAT z faktur sprzedażowych i kosztowych.
        *   Wyliczona jest kwota VAT do zapłaty lub do zwrotu.
        *   Aplikacja generuje dane potrzebne do wypełnienia części ewidencyjnej i deklaracyjnej pliku JPK_V7.

*   **User Story 4.2:** Aplikacja powinna obliczać miesięczną zaliczkę na podatek dochodowy na podstawie przychodów (zgodnie ze stawką ryczałtu).
    *   **Kryteria akceptacji:**
        *   Użytkownik może zdefiniować w ustawieniach swoją stawkę ryczałtu (np. 12%, 8.5%).
        *   Aplikacja sumuje przychody z faktur sprzedażowych i stosuje do nich odpowiednią stawkę.
        *   Wyliczona kwota zaliczki na PIT jest widoczna na pulpicie.

#### **Epic 5: Miesięczne Rozliczenia ZUS**
*Jako użytkownik na ryczałcie, chcę, aby aplikacja automatycznie obliczała moją składkę zdrowotną na podstawie przychodów, uwzględniając obowiązujące progi.*

*   **User Story 5.1:** Aplikacja powinna śledzić mój narastający roczny przychód i na tej podstawie określać podstawę wymiaru składki zdrowotnej.
    *   **Kryteria akceptacji:**
        *   Aplikacja monitoruje przychód w odniesieniu do progów: do 60 000 zł, 60 000 - 300 000 zł, powyżej 300 000 zł.
        *   Po przekroczeniu progu, aplikacja automatycznie stosuje wyższą podstawę do obliczenia składki zdrowotnej od kolejnego miesiąca.
        *   Użytkownik jest informowany o zbliżaniu się do kolejnego progu.

*   **User Story 5.2:** Aplikacja powinna wyświetlać pełną kwotę do zapłaty do ZUS (składki społeczne + składka zdrowotna).
    *   **Kryteria akceptacji:**
        *   Użytkownik może zdefiniować, jakie składki społeczne opłaca (np. tylko zdrowotne, preferencyjny ZUS, duży ZUS).
        *   Na pulpicie głównym widoczna jest jedna, łączna kwota do przelewu na indywidualny rachunek składkowy ZUS.

---

### 4. Wymagania Niefunkcjonalne

*   **Bezpieczeństwo:** Wrażliwe dane (tokeny KSeF) muszą być szyfrowane. Cała komunikacja z API musi odbywać się przez HTTPS.
*   **Niezawodność:** Aplikacja musi poprawnie obsługiwać błędy i wyjątki z API KSeF (np. błędy walidacji, niedostępność usługi) i informować o nich użytkownika w zrozumiały sposób.
*   **Użyteczność:** Interfejs musi być prosty, czysty i intuicyjny, zaprojektowany z myślą o osobach niebędących księgowymi.
*   **Zgodność:** Logika biznesowa dotycząca obliczeń podatkowych i ZUS musi być na bieżąco aktualizowana zgodnie ze zmianami w polskim prawie.

---

### 5. Ograniczenia i Założenia

*   **Ograniczenie:** Aplikacja jest w pełni zależna od dostępności i specyfikacji API KSeF. Każda zmiana w API może wymagać aktualizacji aplikacji.
*   **Ograniczenie:** Aplikacja nie ponosi odpowiedzialności za ostateczne rozliczenia użytkownika. Stanowi narzędzie wspomagające, a wprowadzone dane i wygenerowane wyniki powinny być weryfikowane. W aplikacji musi znaleźć się odpowiednia klauzula (disclaimer).
*   **Założenie:** Użytkownik posiada aktywne konto w KSeF oraz środek do autoryzacji (np. Profil Zaufany, podpis kwalifikowany).

---

### 6. Kryteria Sukcesu

*   **Miernik 1 (Adopcja):** Liczba aktywnych użytkowników miesięcznie.
*   **Miernik 2 (Automatyzacja):** 90% faktur przychodowych i kosztowych jest przetwarzanych przez aplikację bez ręcznej interwencji.
*   **Miernik 3 (Satysfakcja):** Użytkownicy w ankietach oceniają, że aplikacja oszczędza im co najmniej 2 godziny pracy miesięcznie.